use crate::matching::OrderBook;
use crate::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade};
use crate::{BacktestError, Result};
use std::collections::HashMap;
use tokio::sync::{broadcast, mpsc};
use tracing::{debug, error, info, warn};

/// 撮合引擎
pub struct MatchingEngine {
    /// 订单簿
    orderbook: OrderBook,
    /// 待处理订单
    pending_orders: HashMap<String, Order>,
    /// 市场数据输入
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 订单输入
    order_rx: mpsc::Receiver<Order>,
    /// 成交输出
    trade_tx: broadcast::Sender<Trade>,
    /// 订单状态更新输出
    order_update_tx: broadcast::Sender<Order>,
    /// 市场数据转发输出（给WebSocket服务器）
    market_data_forward_tx: broadcast::Sender<MarketData>,
}

impl MatchingEngine {
    /// 创建新的撮合引擎
    pub fn new(
        market_data_rx: broadcast::Receiver<MarketData>,
        order_rx: mpsc::Receiver<Order>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
        market_data_forward_tx: broadcast::Sender<MarketData>,
    ) -> Self {
        Self {
            orderbook: OrderBook::new(),
            pending_orders: HashMap::new(),
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        }
    }

    /// 启动撮合引擎
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting matching engine");

        // 检查订单通道是否已关闭
        let mut order_channel_closed = false;

        loop {
            tokio::select! {
                // 处理市场数据
                market_data = self.market_data_rx.recv() => {
                    match market_data {
                        Ok(data) => {
                            if let Err(e) = self.process_market_data(data).await {
                                error!("Failed to process market data: {}", e);
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Market data channel closed");
                            break;
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            debug!("Market data lagged, skipped {} messages", skipped);
                        }
                    }
                }

                // 处理新订单（如果通道还开着）
                order = self.order_rx.recv(), if !order_channel_closed => {
                    match order {
                        Some(order) => {
                            if let Err(e) = self.process_order(order).await {
                                error!("Failed to process order: {}", e);
                            }
                        }
                        None => {
                            info!("Order channel closed");
                            order_channel_closed = true;
                            // 不退出循环，继续处理市场数据
                        }
                    }
                }
            }
        }

        info!("Matching engine stopped");
        Ok(())
    }

    /// 处理市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        // 先处理撮合逻辑
        match &market_data {
            MarketData::OrderBook(snapshot) => {
                debug!("Processing orderbook snapshot");
                // 重建订单簿
                self.orderbook
                    .rebuild_from_snapshot(&snapshot.bids, &snapshot.asks);
                // 尝试撮合待处理订单
                self.match_pending_orders().await?;
            }
            MarketData::Bbo(bbo) => {
                debug!("Processing BBO update");
                // 使用BBO进行撮合
                self.match_with_bbo(bbo).await?;
            }
            MarketData::Trade(trade) => {
                debug!("Processing external trade: {}", trade.id);
                // 外部交易可能影响订单簿状态
                // 这里可以添加相应的处理逻辑
            }
            MarketData::BookTicker(bookticker) => {
                debug!(
                    "Processing BookTicker data: update_id={}",
                    bookticker.update_id
                );
                // 将BookTicker转换为BBO进行撮合
                let bbo = bookticker.to_bbo();
                self.match_with_bbo(&bbo).await?;
            }

            MarketData::TradeData(trade_data) => {
                debug!("Processing TradeData: id={}", trade_data.id);
                // 交易数据通常用于验证撮合结果，这里可以添加相关逻辑
            }
        }

        // 处理完撮合后，将市场数据转发给WebSocket服务器
        if let Err(e) = self.market_data_forward_tx.send(market_data) {
            debug!("Failed to forward market data to WebSocket: {}", e);
            // 这里不返回错误，因为WebSocket转发失败不应该影响撮合引擎的正常运行
        }

        Ok(())
    }

    /// 处理新订单
    async fn process_order(&mut self, mut order: Order) -> Result<()> {
        info!(
            "Processing order: {} {} {} @ {:?}",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            order.quantity,
            order.price
        );

        match order.order_type {
            OrderType::Market => {
                // 市价单立即撮合
                self.match_market_order(&mut order).await?;
            }
            OrderType::Limit => {
                // 限价单先尝试撮合，未成交部分加入订单簿
                self.match_limit_order(&mut order).await?;
            }
        }

        Ok(())
    }

    /// 撮合市价单
    async fn match_market_order(&mut self, order: &mut Order) -> Result<()> {
        // 市价单需要设置一个极端价格来确保能够撮合
        let market_price = match order.side {
            OrderSide::Buy => {
                // 买入市价单设置极高价格
                if let Some(best_ask) = self.orderbook.best_ask() {
                    Price::new(best_ask.value() * 10.0) // 设置为最佳卖价的10倍
                } else {
                    Price::new(f64::MAX)
                }
            }
            OrderSide::Sell => {
                // 卖出市价单设置极低价格
                if let Some(best_bid) = self.orderbook.best_bid() {
                    Price::new(best_bid.value() * 0.1) // 设置为最佳买价的0.1倍
                } else {
                    Price::new(0.0)
                }
            }
        };

        // 临时设置市价单的价格用于撮合
        let mut market_order = order.clone();
        market_order.price = Some(market_price);

        // 使用订单簿的FIFO撮合功能
        match self.orderbook.match_order(&market_order) {
            Ok((matches, remaining_quantity)) => {
                // 处理所有成交
                for (matched_order, trade_price, trade_quantity) in matches {
                    // self.execute_trade(order, trade_price, trade_quantity)
                    //     .await?;

                    // 更新被撮合订单的状态
                    let mut updated_order = matched_order;
                    if updated_order.quantity <= 0.0 {
                        updated_order.status = OrderStatus::Filled;
                    } else {
                        updated_order.status = OrderStatus::PartiallyFilled;
                    }
                    self.send_order_update(updated_order).await?;
                }

                // 更新原订单状态
                if remaining_quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                } else {
                    order.status = OrderStatus::PartiallyFilled;
                    order.quantity = remaining_quantity;
                }
            }
            Err(e) => {
                warn!("Failed to match market order {}: {}", order.id, e);
                order.status = OrderStatus::Cancelled;
            }
        }

        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 撮合限价单
    async fn match_limit_order(&mut self, order: &mut Order) -> Result<()> {
        let _order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("Limit order must have price".to_string()))?;

        // 使用订单簿的FIFO撮合功能
        match self.orderbook.match_order(order) {
            Ok((matches, remaining_quantity)) => {
                info!("Matched limit orders: {:?}", matches);
                // 处理所有成交
                for (matched_order, trade_price, trade_quantity) in matches {
                    // self.execute_trade(order, trade_price, trade_quantity)
                    //     .await?;

                    // 更新被撮合订单的状态
                    let mut updated_order = matched_order;
                    if updated_order.quantity <= 0.0 {
                        updated_order.status = OrderStatus::Filled;
                        // 从pending_orders中移除已完成的订单
                        self.pending_orders.remove(&updated_order.id);
                    } else {
                        updated_order.status = OrderStatus::PartiallyFilled;
                        // 更新pending_orders中的订单
                        self.pending_orders
                            .insert(updated_order.id.clone(), updated_order.clone());
                    }
                    self.send_order_update(updated_order).await?;
                }

                // 更新原订单状态
                if remaining_quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                } else if remaining_quantity < order.quantity {
                    order.status = OrderStatus::PartiallyFilled;
                    order.quantity = remaining_quantity;
                    // 未成交部分加入订单簿和待处理订单
                    self.orderbook.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                } else {
                    // 完全未成交，直接加入订单簿和待处理订单
                    order.status = OrderStatus::Pending;
                    self.orderbook.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                }
            }
            Err(e) => {
                warn!("Failed to match limit order {}: {}", order.id, e);
                // 撮合失败，将订单加入订单簿
                order.status = OrderStatus::Pending;
                self.orderbook.add_order(order.clone());
                self.pending_orders.insert(order.id.clone(), order.clone());
            }
        }

        info!("Sending order update: {:?}", order);
        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 使用BBO进行撮合
    async fn match_with_bbo(&mut self, bbo: &Bbo) -> Result<()> {
        // 收集需要处理的订单信息
        let mut orders_to_process = Vec::new();

        for (order_id, order) in &self.pending_orders {
            let order_price = order.price.unwrap_or(Price::new(0.0));
            let can_match = match order.side {
                OrderSide::Buy => order_price >= bbo.ask_price,
                OrderSide::Sell => order_price <= bbo.bid_price,
            };

            if can_match {
                orders_to_process.push((order_id.clone(), order.clone()));
            }
        }

        // 处理匹配的订单
        let mut orders_to_remove = Vec::new();

        for (order_id, mut order) in orders_to_process {
            let trade_price = match order.side {
                OrderSide::Buy => bbo.ask_price,
                OrderSide::Sell => bbo.bid_price,
            };

            let available_quantity = match order.side {
                OrderSide::Buy => bbo.ask_quantity,
                OrderSide::Sell => bbo.bid_quantity,
            };

            let trade_quantity = order.quantity.min(available_quantity);
            if trade_quantity > 0.0 {
                // 执行交易
                // self.execute_trade(&order, trade_price, trade_quantity)
                //     .await?;

                // 更新订单状态
                order.quantity -= trade_quantity;
                if order.quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                    orders_to_remove.push(order_id.clone());
                } else {
                    order.status = OrderStatus::PartiallyFilled;
                }

                // 更新订单到pending_orders
                if let Some(pending_order) = self.pending_orders.get_mut(&order_id) {
                    *pending_order = order.clone();
                }

                // 发送订单更新
                info!("Sending order update: {:?}", order);
                self.send_order_update(order).await?;
            }
        }

        // 移除已完成的订单
        for order_id in orders_to_remove {
            self.pending_orders.remove(&order_id);
            self.orderbook.remove_order(&order_id);
        }

        Ok(())
    }

    /// 撮合待处理订单
    async fn match_pending_orders(&mut self) -> Result<()> {
        // 收集所有待处理订单的ID，避免在迭代时修改HashMap
        let pending_order_ids: Vec<String> = self.pending_orders.keys().cloned().collect();

        for order_id in pending_order_ids {
            if let Some(order) = self.pending_orders.get(&order_id).cloned() {
                // 尝试重新撮合每个待处理订单
                match self.orderbook.match_order(&order) {
                    Ok((matches, remaining_quantity)) => {
                        // 处理所有新的成交
                        for (matched_order, trade_price, trade_quantity) in matches {
                            // self.execute_trade(&order, trade_price, trade_quantity)
                            //     .await?;

                            // 更新被撮合订单的状态
                            let mut updated_order = matched_order;
                            if updated_order.quantity <= 0.0 {
                                updated_order.status = OrderStatus::Filled;
                                self.pending_orders.remove(&updated_order.id);
                            } else {
                                updated_order.status = OrderStatus::PartiallyFilled;
                                self.pending_orders
                                    .insert(updated_order.id.clone(), updated_order.clone());
                            }
                            self.send_order_update(updated_order).await?;
                        }

                        // 更新原订单状态
                        if let Some(pending_order) = self.pending_orders.get(&order_id).cloned() {
                            let mut updated_order = pending_order;
                            if remaining_quantity <= 0.0 {
                                updated_order.status = OrderStatus::Filled;
                                self.pending_orders.remove(&order_id);
                            } else if remaining_quantity < updated_order.quantity {
                                updated_order.status = OrderStatus::PartiallyFilled;
                                updated_order.quantity = remaining_quantity;
                                self.pending_orders
                                    .insert(order_id.clone(), updated_order.clone());
                            }
                            self.send_order_update(updated_order).await?;
                        }
                    }
                    Err(_) => {
                        // 撮合失败，保持订单在待处理状态
                        continue;
                    }
                }
            }
        }

        Ok(())
    }

    /// 执行交易
    async fn execute_trade(&self, order: &Order, price: Price, quantity: f64) -> Result<()> {
        let trade = Trade {
            id: format!(
                "{}_{}",
                order.id,
                chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0)
            ),
            price,
            quantity,
            side: order.side.clone(),
            timestamp: Some(chrono::Utc::now()),
        };

        info!(
            "Trade executed: {} {} @ {} (quantity: {})",
            trade.id,
            match trade.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            trade.price,
            trade.quantity
        );

        if let Err(e) = self.trade_tx.send(trade) {
            error!("Failed to send trade: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send trade: {}",
                e
            )));
        }

        Ok(())
    }

    /// 发送订单更新
    async fn send_order_update(&self, order: Order) -> Result<()> {
        if let Err(e) = self.order_update_tx.send(order) {
            error!("Failed to send order update: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send order update: {}",
                e
            )));
        }

        Ok(())
    }

    /// 取消订单
    pub async fn cancel_order(&mut self, order_id: &str) -> Result<()> {
        if let Some(mut order) = self.pending_orders.remove(order_id) {
            order.status = OrderStatus::Cancelled;
            self.orderbook.remove_order(order_id);
            self.send_order_update(order).await?;
            info!("Order cancelled: {}", order_id);
        }

        Ok(())
    }

    /// 获取订单簿快照
    pub fn get_orderbook_snapshot(
        &self,
    ) -> (
        std::collections::BTreeMap<Price, f64>,
        std::collections::BTreeMap<Price, f64>,
    ) {
        self.orderbook.snapshot()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{OrderStatus, OrderType};
    use chrono::Utc;
    use tokio::sync::{broadcast, mpsc};

    fn create_test_order(id: &str, side: OrderSide, price: Option<Price>, quantity: f64) -> Order {
        Order {
            id: id.to_string(),
            order_type: OrderType::Limit,
            side,
            price,
            quantity,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        }
    }

    fn create_market_order(id: &str, side: OrderSide, quantity: f64) -> Order {
        Order {
            id: id.to_string(),
            order_type: OrderType::Market,
            side,
            price: None,
            quantity,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_matching_engine_limit_order_matching() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let mut engine = MatchingEngine::new(
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 10.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建买单进行撮合
        let mut buy_order = create_test_order("buy1", OrderSide::Buy, Some(Price::new(100.0)), 5.0);

        // 执行撮合
        let result = engine.match_limit_order(&mut buy_order).await;
        assert!(result.is_ok());

        // 验证买单状态
        assert_eq!(buy_order.status, OrderStatus::Filled);

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 5.0);
        assert_eq!(trade.price, Price::new(100.0));

        // 验证订单更新
        let order_update = order_update_rx.try_recv();
        assert!(order_update.is_ok());
    }

    #[tokio::test]
    async fn test_matching_engine_market_order() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let mut engine = MatchingEngine::new(
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 10.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建市价买单
        let mut market_buy_order = create_market_order("buy1", OrderSide::Buy, 3.0);

        // 执行撮合
        let result = engine.match_market_order(&mut market_buy_order).await;
        assert!(result.is_ok());

        // 验证市价单状态
        assert_eq!(market_buy_order.status, OrderStatus::Filled);

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 3.0);
        assert_eq!(trade.price, Price::new(100.0));
    }

    #[tokio::test]
    async fn test_matching_engine_partial_fill() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let mut engine = MatchingEngine::new(
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加小的卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 3.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建大的买单进行撮合
        let mut buy_order =
            create_test_order("buy1", OrderSide::Buy, Some(Price::new(100.0)), 10.0);

        // 执行撮合
        let result = engine.match_limit_order(&mut buy_order).await;
        assert!(result.is_ok());

        // 验证买单状态（应该是部分成交）
        assert_eq!(buy_order.status, OrderStatus::PartiallyFilled);
        assert_eq!(buy_order.quantity, 7.0); // 剩余7.0未成交

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 3.0); // 成交3.0

        // 验证买单被加入到待处理订单
        assert!(engine.pending_orders.contains_key("buy1"));
    }

    #[tokio::test]
    async fn test_matching_engine_cancel_order() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let mut engine = MatchingEngine::new(
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加订单到待处理列表
        let order = create_test_order("test1", OrderSide::Buy, Some(Price::new(99.0)), 10.0);
        engine
            .pending_orders
            .insert("test1".to_string(), order.clone());
        engine.orderbook.add_order(order);

        // 取消订单
        let result = engine.cancel_order("test1").await;
        assert!(result.is_ok());

        // 验证订单被移除
        assert!(!engine.pending_orders.contains_key("test1"));

        // 验证订单更新通知
        let order_update = order_update_rx.try_recv();
        assert!(order_update.is_ok());
        let updated_order = order_update.unwrap();
        assert_eq!(updated_order.status, OrderStatus::Cancelled);
    }
}
