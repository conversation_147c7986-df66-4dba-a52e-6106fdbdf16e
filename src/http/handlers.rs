use crate::config::ConfigManager;
use crate::types::{OrderBookSnapshot, Trade};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::convert::Infallible;
use warp::{http::StatusCode, Reply};

/// API响应结构
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
        }
    }
}

/// 健康检查响应
#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub version: String,
}

/// 配置信息响应
#[derive(Debug, Serialize)]
pub struct ConfigResponse {
    pub exchange: String,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: chrono::DateTime<chrono::Utc>,
    pub websocket_port: u16,
    pub http_port: u16,
}

/// Binance API 兼容的交易所信息响应
#[derive(Debug, Serialize)]
pub struct ExchangeInfoResponse {
    pub timezone: String,
    #[serde(rename = "serverTime")]
    pub server_time: i64,
    #[serde(rename = "futuresType")]
    pub futures_type: String,
    #[serde(rename = "rateLimits")]
    pub rate_limits: Vec<RateLimit>,
    #[serde(rename = "exchangeFilters")]
    pub exchange_filters: Vec<serde_json::Value>,
    pub assets: Vec<AssetInfo>,
    pub symbols: Vec<SymbolInfo>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sors: Option<Vec<SorInfo>>,
}

/// 资产信息
#[derive(Debug, Serialize)]
pub struct AssetInfo {
    pub asset: String,
    #[serde(rename = "marginAvailable")]
    pub margin_available: bool,
    #[serde(rename = "autoAssetExchange")]
    pub auto_asset_exchange: String,
}

/// SOR (Smart Order Routing) 信息
#[derive(Debug, Serialize)]
pub struct SorInfo {
    #[serde(rename = "baseAsset")]
    pub base_asset: String,
    pub symbols: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct RateLimit {
    #[serde(rename = "rateLimitType")]
    pub rate_limit_type: String,
    pub interval: String,
    #[serde(rename = "intervalNum")]
    pub interval_num: i32,
    pub limit: i32,
}

#[derive(Debug, Serialize)]
pub struct SymbolInfo {
    pub symbol: String,
    pub pair: String,
    #[serde(rename = "contractType")]
    pub contract_type: String,
    #[serde(rename = "deliveryDate")]
    pub delivery_date: i64,
    #[serde(rename = "onboardDate")]
    pub onboard_date: i64,
    pub status: String,
    #[serde(rename = "maintMarginPercent")]
    pub maint_margin_percent: String,
    #[serde(rename = "requiredMarginPercent")]
    pub required_margin_percent: String,
    #[serde(rename = "baseAsset")]
    pub base_asset: String,
    #[serde(rename = "quoteAsset")]
    pub quote_asset: String,
    #[serde(rename = "marginAsset")]
    pub margin_asset: String,
    #[serde(rename = "pricePrecision")]
    pub price_precision: i32,
    #[serde(rename = "quantityPrecision")]
    pub quantity_precision: i32,
    #[serde(rename = "baseAssetPrecision")]
    pub base_asset_precision: i32,
    #[serde(rename = "quotePrecision")]
    pub quote_precision: i32,
    #[serde(rename = "underlyingType")]
    pub underlying_type: String,
    #[serde(rename = "underlyingSubType")]
    pub underlying_sub_type: Vec<String>,
    #[serde(rename = "triggerProtect")]
    pub trigger_protect: String,
    #[serde(rename = "liquidationFee")]
    pub liquidation_fee: String,
    #[serde(rename = "marketTakeBound")]
    pub market_take_bound: String,
    #[serde(rename = "maxMoveOrderLimit")]
    pub max_move_order_limit: i32,
    pub filters: Vec<serde_json::Value>,
    #[serde(rename = "orderTypes")]
    pub order_types: Vec<String>,
    #[serde(rename = "timeInForce")]
    pub time_in_force: Vec<String>,
    #[serde(rename = "permissionSets")]
    pub permission_sets: Vec<String>,
}

/// 统计信息响应
#[derive(Debug, Serialize)]
pub struct StatsResponse {
    pub connected_clients: usize,
    pub total_trades: u64,
    pub orderbook_updates: u64,
    pub uptime_seconds: u64,
}

/// 数据流状态响应
#[derive(Debug, Serialize)]
pub struct DataStreamStatusResponse {
    pub status: String,
    pub config: DataStreamConfigResponse,
    pub stats: DataStreamStatsResponse,
}

/// 数据流配置响应
#[derive(Debug, Serialize, Deserialize)]
pub struct DataStreamConfigResponse {
    pub read_interval_ms: u64,
    pub realtime_simulation: bool,
    pub buffer_size: usize,
}

/// 数据流统计响应
#[derive(Debug, Serialize)]
pub struct DataStreamStatsResponse {
    pub messages_processed: u64,
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    pub last_processed_time: Option<chrono::DateTime<chrono::Utc>>,
    pub error_count: u64,
}

/// 创建符合Binance API的symbol filters
fn create_symbol_filters() -> Vec<serde_json::Value> {
    vec![
        serde_json::json!({
            "filterType": "PRICE_FILTER",
            "minPrice": "0.00000100",
            "maxPrice": "1000000.00000000",
            "tickSize": "0.00000100"
        }),
        serde_json::json!({
            "filterType": "PERCENT_PRICE",
            "multiplierUp": "5",
            "multiplierDown": "0.2",
            "avgPriceMins": 5
        }),
        serde_json::json!({
            "filterType": "LOT_SIZE",
            "minQty": "0.00000100",
            "maxQty": "9000.00000000",
            "stepSize": "0.00000100"
        }),
        serde_json::json!({
            "filterType": "MIN_NOTIONAL",
            "minNotional": "10.00000000",
            "applyToMarket": true,
            "avgPriceMins": 5
        }),
        serde_json::json!({
            "filterType": "ICEBERG_PARTS",
            "limit": 10
        }),
        serde_json::json!({
            "filterType": "MARKET_LOT_SIZE",
            "minQty": "0.00000000",
            "maxQty": "922.32720000",
            "stepSize": "0.00000000"
        }),
        serde_json::json!({
            "filterType": "MAX_NUM_ORDERS",
            "maxNumOrders": 200
        }),
        serde_json::json!({
            "filterType": "MAX_NUM_ALGO_ORDERS",
            "maxNumAlgoOrders": 5
        }),
        serde_json::json!({
            "filterType": "MAX_NUM_ICEBERG_ORDERS",
            "maxNumIcebergOrders": 5
        }),
        serde_json::json!({
            "filterType": "MAX_POSITION",
            "maxPosition": "10.00000000"
        }),
        serde_json::json!({
            "filterType": "TRAILING_DELTA",
            "minTrailingAboveDelta": 10,
            "maxTrailingAboveDelta": 2000,
            "minTrailingBelowDelta": 10,
            "maxTrailingBelowDelta": 2000
        }),
    ]
}

/// 创建符合Binance Futures API的symbol filters
fn create_futures_symbol_filters() -> Vec<serde_json::Value> {
    vec![
        serde_json::json!({
            "maxPrice": "4529764",
            "tickSize": "0.10",
            "filterType": "PRICE_FILTER",
            "minPrice": "556.80"
        }),
        serde_json::json!({
            "stepSize": "0.001",
            "minQty": "0.001",
            "filterType": "LOT_SIZE",
            "maxQty": "1000"
        }),
        serde_json::json!({
            "minQty": "0.001",
            "stepSize": "0.001",
            "filterType": "MARKET_LOT_SIZE",
            "maxQty": "120"
        }),
        serde_json::json!({
            "limit": 200,
            "filterType": "MAX_NUM_ORDERS"
        }),
        serde_json::json!({
            "filterType": "MAX_NUM_ALGO_ORDERS",
            "limit": 10
        }),
        serde_json::json!({
            "notional": "100",
            "filterType": "MIN_NOTIONAL"
        }),
        serde_json::json!({
            "multiplierUp": "1.0500",
            "multiplierDown": "0.9500",
            "filterType": "PERCENT_PRICE",
            "multiplierDecimal": "4"
        }),
        serde_json::json!({
            "positionControlSide": "NONE",
            "filterType": "POSITION_RISK_CONTROL"
        }),
    ]
}

/// 创建符合Binance API的exchange filters
fn create_exchange_filters() -> Vec<serde_json::Value> {
    vec![
        serde_json::json!({
            "filterType": "EXCHANGE_MAX_NUM_ORDERS",
            "maxNumOrders": 1000
        }),
        serde_json::json!({
            "filterType": "EXCHANGE_MAX_NUM_ALGO_ORDERS",
            "maxNumAlgoOrders": 200
        }),
        serde_json::json!({
            "filterType": "EXCHANGE_MAX_NUM_ICEBERG_ORDERS",
            "maxNumIcebergOrders": 10000
        }),
    ]
}

/// 健康检查处理器
pub async fn health_handler() -> Result<impl Reply, Infallible> {
    tracing::info!("🔍 Health check request received");
    let response = ApiResponse::success(HealthResponse {
        status: "healthy".to_string(),
        timestamp: chrono::Utc::now(),
        version: env!("CARGO_PKG_VERSION").to_string(),
    });

    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取配置信息处理器
pub async fn config_handler() -> Result<impl Reply, Infallible> {
    match ConfigManager::get() {
        Ok(config) => {
            let response = ApiResponse::success(ConfigResponse {
                exchange: format!("{:?}", config.exchange),
                start_time: config.start_time,
                end_time: config.end_time,
                websocket_port: config.websocket_port,
                http_port: config.http_port,
            });

            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::OK,
            ))
        }
        Err(e) => {
            let response: ApiResponse<ConfigResponse> =
                ApiResponse::error(format!("Failed to get config: {}", e));
            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::INTERNAL_SERVER_ERROR,
            ))
        }
    }
}

/// Binance API 兼容的交易所信息处理器
pub async fn exchange_info_handler() -> Result<impl Reply, Infallible> {
    tracing::info!("🔍 Exchange info request received at /fapi/v1/exchangeInfo");
    let server_time = chrono::Utc::now().timestamp_millis();

    // 创建模拟的交易所信息，兼容 Binance API
    let exchange_info = ExchangeInfoResponse {
        timezone: "UTC".to_string(),
        server_time,
        futures_type: "U_MARGINED".to_string(),
        rate_limits: vec![
            RateLimit {
                rate_limit_type: "REQUEST_WEIGHT".to_string(),
                interval: "MINUTE".to_string(),
                interval_num: 1,
                limit: 2400,
            },
            RateLimit {
                rate_limit_type: "ORDERS".to_string(),
                interval: "MINUTE".to_string(),
                interval_num: 1,
                limit: 1200,
            },
            RateLimit {
                rate_limit_type: "ORDERS".to_string(),
                interval: "SECOND".to_string(),
                interval_num: 10,
                limit: 300,
            },
        ],
        exchange_filters: vec![], // Binance futures API returns empty array
        assets: vec![
            AssetInfo {
                asset: "USDT".to_string(),
                margin_available: true,
                auto_asset_exchange: "-10000".to_string(),
            },
            AssetInfo {
                asset: "BTC".to_string(),
                margin_available: true,
                auto_asset_exchange: "-0.10000000".to_string(),
            },
            AssetInfo {
                asset: "BNB".to_string(),
                margin_available: true,
                auto_asset_exchange: "0".to_string(),
            },
            AssetInfo {
                asset: "ETH".to_string(),
                margin_available: true,
                auto_asset_exchange: "0".to_string(),
            },
        ],
        symbols: vec![SymbolInfo {
            symbol: "BTCUSDT".to_string(),
            pair: "BTCUSDT".to_string(),
            contract_type: "PERPETUAL".to_string(),
            delivery_date: 4133404800000,
            onboard_date: 1569398400000,
            status: "TRADING".to_string(),
            maint_margin_percent: "2.5000".to_string(),
            required_margin_percent: "5.0000".to_string(),
            base_asset: "BTC".to_string(),
            quote_asset: "USDT".to_string(),
            margin_asset: "USDT".to_string(),
            price_precision: 2,
            quantity_precision: 3,
            base_asset_precision: 8,
            quote_precision: 8,
            underlying_type: "COIN".to_string(),
            underlying_sub_type: vec!["PoW".to_string()],
            trigger_protect: "0.0500".to_string(),
            liquidation_fee: "0.012500".to_string(),
            market_take_bound: "0.05".to_string(),
            max_move_order_limit: 10000,
            filters: create_futures_symbol_filters(),
            order_types: vec![
                "LIMIT".to_string(),
                "MARKET".to_string(),
                "STOP".to_string(),
                "STOP_MARKET".to_string(),
                "TAKE_PROFIT".to_string(),
                "TAKE_PROFIT_MARKET".to_string(),
                "TRAILING_STOP_MARKET".to_string(),
            ],
            time_in_force: vec![
                "GTC".to_string(),
                "IOC".to_string(),
                "FOK".to_string(),
                "GTX".to_string(),
                "GTD".to_string(),
            ],
            permission_sets: vec!["GRID".to_string(), "COPY".to_string()],
        }],
        sors: Some(vec![SorInfo {
            base_asset: "BTC".to_string(),
            symbols: vec!["BTCUSDT".to_string(), "BTCUSDC".to_string()],
        }]),
    };

    // 直接返回exchangeInfo对象，与真实Binance API保持一致
    Ok(warp::reply::with_status(
        warp::reply::json(&exchange_info),
        StatusCode::OK,
    ))
}

/// 获取订单簿快照处理器
pub async fn orderbook_handler() -> Result<impl Reply, Infallible> {
    // TODO: 从撮合引擎获取实际的订单簿数据
    // 这里是占位实现
    let snapshot = OrderBookSnapshot {
        timestamp: chrono::Utc::now(),
        update_id: Some(12345),
        bids: std::collections::BTreeMap::new(),
        asks: std::collections::BTreeMap::new(),
    };

    let response = ApiResponse::success(snapshot);
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

pub async fn listen_key_handler() -> Result<impl Reply, Infallible> {
    let body = serde_json::json!({
        "listenKey": "test_listen_key"
    });
    Ok(warp::reply::with_status(
        warp::reply::json(&body),
        StatusCode::OK,
    ))
}

/// 获取最近交易处理器
pub async fn trades_handler() -> Result<impl Reply, Infallible> {
    // TODO: 从撮合引擎获取实际的交易数据
    // 这里是占位实现
    let trades: Vec<Trade> = Vec::new();

    let response = ApiResponse::success(trades);
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取统计信息处理器
pub async fn stats_handler() -> Result<impl Reply, Infallible> {
    // TODO: 从各个模块收集实际的统计数据
    // 这里是占位实现
    let stats = StatsResponse {
        connected_clients: 0,
        total_trades: 0,
        orderbook_updates: 0,
        uptime_seconds: 0,
    };

    let response = ApiResponse::success(stats);
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取技术指标处理器
pub async fn indicators_handler() -> Result<impl Reply, Infallible> {
    // TODO: 从技术指标模块获取数据
    // 这里是占位实现
    let indicators: HashMap<String, f64> = HashMap::new();

    let response = ApiResponse::success(indicators);
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取数据流状态处理器
pub async fn datastream_status_handler() -> Result<impl Reply, Infallible> {
    if let Some(controller) = crate::state::get_data_stream_controller().await {
        let ctl = controller.lock().await;
        let status = ctl.get_status().await;
        let config = ctl.get_config().await;
        let stats = ctl.get_stats().await;

        let status_str = match status {
            crate::data::DataStreamStatus::Stopped => "stopped",
            crate::data::DataStreamStatus::Running => "running",
            crate::data::DataStreamStatus::Paused => "paused",
            crate::data::DataStreamStatus::Error(_) => "error",
        }
        .to_string();

        let status_response = DataStreamStatusResponse {
            status: status_str,
            config: DataStreamConfigResponse {
                read_interval_ms: config.read_interval_ms,
                realtime_simulation: config.realtime_simulation,
                buffer_size: config.buffer_size,
            },
            stats: DataStreamStatsResponse {
                messages_processed: stats.messages_processed,
                start_time: stats.start_time,
                last_processed_time: stats.last_processed_time,
                error_count: stats.error_count,
            },
        };

        let response = ApiResponse::success(status_response);
        Ok(warp::reply::with_status(
            warp::reply::json(&response),
            StatusCode::OK,
        ))
    } else {
        let response: ApiResponse<()> =
            ApiResponse::error("Data stream controller not available".to_string());
        Ok(warp::reply::with_status(
            warp::reply::json(&response),
            StatusCode::INTERNAL_SERVER_ERROR,
        ))
    }
}

/// 启动数据流处理器
pub async fn datastream_start_handler() -> Result<impl Reply, Infallible> {
    if let Some(controller) = crate::state::get_data_stream_controller().await {
        match controller.lock().await.start().await {
            Ok(_) => {
                let response = ApiResponse::success("Data stream started successfully");
                Ok(warp::reply::with_status(
                    warp::reply::json(&response),
                    StatusCode::OK,
                ))
            }
            Err(e) => {
                let response: ApiResponse<()> =
                    ApiResponse::error(format!("Failed to start data stream: {}", e));
                Ok(warp::reply::with_status(
                    warp::reply::json(&response),
                    StatusCode::INTERNAL_SERVER_ERROR,
                ))
            }
        }
    } else {
        let response: ApiResponse<()> =
            ApiResponse::error("Data stream controller not available".to_string());
        Ok(warp::reply::with_status(
            warp::reply::json(&response),
            StatusCode::INTERNAL_SERVER_ERROR,
        ))
    }
}

/// 停止数据流处理器
pub async fn datastream_stop_handler() -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器停止方法
    // 这里是占位实现
    let response = ApiResponse::success("Data stream stop command sent");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 暂停数据流处理器
pub async fn datastream_pause_handler() -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器暂停方法
    // 这里是占位实现
    let response = ApiResponse::success("Data stream pause command sent");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 恢复数据流处理器
pub async fn datastream_resume_handler() -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器恢复方法
    // 这里是占位实现
    let response = ApiResponse::success("Data stream resume command sent");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 更新数据流配置处理器
pub async fn datastream_config_handler(
    config: DataStreamConfigResponse,
) -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器更新配置方法
    // 这里是占位实现
    tracing::info!("Received config update: {:?}", config);
    let response = ApiResponse::success("Data stream configuration updated");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 错误处理器
pub async fn handle_rejection(err: warp::Rejection) -> Result<impl Reply, Infallible> {
    let code;
    let message;

    if err.is_not_found() {
        code = StatusCode::NOT_FOUND;
        message = "Not Found";
    } else if let Some(_) = err.find::<warp::filters::body::BodyDeserializeError>() {
        code = StatusCode::BAD_REQUEST;
        message = "Invalid Body";
    } else if let Some(_) = err.find::<warp::reject::MethodNotAllowed>() {
        code = StatusCode::METHOD_NOT_ALLOWED;
        message = "Method Not Allowed";
    } else {
        tracing::error!("Unhandled rejection: {:?}", err);
        code = StatusCode::INTERNAL_SERVER_ERROR;
        message = "Internal Server Error";
    }

    let response: ApiResponse<()> = ApiResponse::error(message.to_string());
    Ok(warp::reply::with_status(warp::reply::json(&response), code))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_health_handler() {
        let result = health_handler().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_config_handler() {
        let result = config_handler().await;
        assert!(result.is_ok());
    }
}
